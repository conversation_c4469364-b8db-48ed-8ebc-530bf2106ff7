# Stage 1: Build the application
FROM node:20-alpine AS build


# Set working directory
WORKDIR /app

# Copy package.json and package-lock.json to the container
COPY package.json package-lock.json ./

# Install dependencies using npm install (instead of npm ci, to handle dependency issues)
RUN npm install

# Copy the rest of the application code
COPY . .

# Build the application
RUN npm run build

# Stage 2: Create a lightweight production image
FROM node:20-alpine

# Install MongoDB Database Tools
RUN apk add --no-cache mongodb-tools

# Set working directory
WORKDIR /app

# Copy only necessary files from the previous stage
COPY --from=build /app/dist ./dist
COPY --from=build /app/node_modules ./node_modules
COPY --from=build /app/package.json ./package.json
COPY --from=build /app/.env ./.env

# Copy GCP key file
COPY gcp-key-file.json /app/gcp-key-file.json

# Expose the port the app runs on
EXPOSE 3000

# Command to run the application
CMD ["node", "dist/main"]
