import { Lo<PERSON>, <PERSON><PERSON><PERSON>, OnModuleInit } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { InjectConnection, MongooseModule } from '@nestjs/mongoose';
import { LogsModule } from './modules/logs-module/logs.module';
import { OauthModule } from './ouath/oauth.module';
import { RegisterClientModule } from './modules/register-client/register-client.module';
import { ConfigService } from '@nestjs/config';
import { APP_GUARD } from '@nestjs/core';
import { JwtModule } from '@nestjs/jwt';
import { JwtAuthGuard } from './jwt_guard/api-key.guard';
import { AppController } from './app.controller';
import { Connection } from 'mongoose';
import { BackupController } from './backup/backup.controller';
import { BackupModule } from './backup/backup.module';



@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }), // Load environment variables
    // MongooseModule.forRoot(process.env.MONGO_URI), // Use the MONGO_URI from .env
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        const mongoUrl = configService.get('MONGO_URI');
        return {
          uri: mongoUrl,
          // loggerLevel: 'debug',
          // debug: true,
          useNewUrlParser: true,
          useUnifiedTopology: true,
        };
      },

      inject: [ConfigService],
    }),

    //Secondary Connection
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        uri: configService.get('MONGO_URI_RADIOLOGY'),
        useNewUrlParser: true,
        useUnifiedTopology: true,
      }),
      inject: [ConfigService],
      connectionName: 'radiologyConnection', // Alias
    }),

    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get('JWT_ACCESS_SECRET'),
        signOptions: { expiresIn: '1h' },
      }),
      inject: [ConfigService],
    }),
    LogsModule,
    OauthModule,
    RegisterClientModule,
    BackupModule
  ],
  providers: [
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
  ],
  controllers: [
    AppController,
    BackupController
  ]
})
export class AppModule implements OnModuleInit {
  private readonly logger = new Logger(AppModule.name);
  //Implementation here
  constructor(
    @InjectConnection() private readonly primaryConnection: Connection,
    @InjectConnection('radiologyConnection') private readonly secondaryConnection: Connection,
  ) {}


  onModuleInit() {
    this.primaryConnection.once('open', () => {
      this.logger.log('✅ Connected to Primary MongoDB');
    });

    this.secondaryConnection.once('open', () => {
      this.logger.log('✅ Connected to Secondary MongoDB');
    });

    this.primaryConnection.on('error', (err) => {
      this.logger.error('❌ Primary MongoDB Connection Error:', err);
    });

    this.secondaryConnection.on('error', (err) => {
      this.logger.error('❌ Secondary MongoDB Connection Error:', err);
    });
  }

}