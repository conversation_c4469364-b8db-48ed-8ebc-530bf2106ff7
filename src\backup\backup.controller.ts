import { Controller, Get, Post, HttpStatus } from '@nestjs/common';
import { BackupService } from './backup.service';

@Controller('backup')
export class BackupController {
  constructor(private readonly backupService: BackupService) {}

  @Get()
  async createBackup(): Promise<{ message: string }> {
    const backupPath = await this.backupService.createBackup();
    return { message: `Backup created at ${backupPath}` };
  }

  @Post('with-upload')
  async createBackupWithUpload(): Promise<{ statusCode: number; message: string; url?: string }> {
    try {
      // Create the database backup
      const backupPath = await this.backupService.createBackup();

      // Create a zip file of the backup
      const zipFilePath = await this.backupService.createZipFromBackup(backupPath);

      try {
        // Try to upload the zip file to GCP
        const uploadUrl = await this.backupService.uploadToGCP(zipFilePath);

        return {
          statusCode: HttpStatus.OK,
          message: 'Backup created, zipped, and uploaded successfully',
          url: uploadUrl
        };
      } catch (uploadError) {
        // If GCP upload fails, still return success for the backup and zip creation
        return {
          statusCode: HttpStatus.PARTIAL_CONTENT,
          message: `Backup created and zipped successfully, but upload failed: ${uploadError.message}`
        };
      }
    } catch (error) {
      return {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: `Backup with upload failed: ${error.message}`
      };
    }
  }

  @Post('cleanup')
  async cleanupOldBackups(): Promise<{ statusCode: number; message: string }> {
    try {
      // Manually trigger the cleanup process
      await this.backupService.cleanupOldBackups();

      return {
        statusCode: HttpStatus.OK,
        message: 'Old backups cleaned up successfully'
      };
    } catch (error) {
      return {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: `Failed to clean up old backups: ${error.message}`
      };
    }
  }
}
