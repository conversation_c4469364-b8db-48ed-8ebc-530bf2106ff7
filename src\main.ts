// Import polyfills first
import './polyfills';

import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ConfigService } from '@nestjs/config';
import { Logger} from '@nestjs/common';
//import { JwtMiddleware } from './middleware/api-key.middleware';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const config: ConfigService = app.get(ConfigService);
  const port: number = config.get<number>('PORT');
 // Enable CORS with wildcard '*'
  app.enableCors({
    origin: '*', // Allows requests from all origins
  });

  app.useGlobalPipes(); // Optional for validation
  await app.listen(port, () => {
    Logger.log(`Application listening on port ${port}`, 'Main');
  });
}
bootstrap();
