import { Body, Req, Controller, Get, Post, Query, HttpStatus, UseGuards } from '@nestjs/common';
import { LogsService } from './logs.service';
import { Log } from './schemas/log.schema';
import { JwtAuthGuard } from '../../jwt_guard/api-key.guard';
import { SkipAuth } from '../../jwt_guard/decorators/skipauth.decorators';

@Controller('logs')
export class LogsController {
  constructor(private readonly logsService: LogsService) { }

  @SkipAuth()
  @Post()
  async createLog(@Body() log: Partial<Log>, @Req() req: Request) {
    try {
      const apiKey = req.headers['api-key'];
      const serviceKey = req.headers['servicekey']

      if (!apiKey || apiKey !== process.env.API_SECRET_KEY) {
        return {
          statusCode: HttpStatus.UNAUTHORIZED, // 401 for unauthorized access
          message: 'Invalid API key',
        };
      }
      const createdLog = await this.logsService.createLog(log, serviceKey);

      return {
        statusCode: HttpStatus.CREATED, // 201 for successful creation
        message: 'Log created successfully',
        data: createdLog,
      };
    } catch (error) {
      return {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR, // 500 for server errors
        message: 'Failed to create log',
        error: error.message,
      };
    }
  }

  //No
  @Get()
  async getLogs(@Query() query: Record<string, string | undefined>) {
    try {
      const logs = await this.logsService.getLogs(query);

      return {
        statusCode: HttpStatus.OK, // 200 for successful retrieval
        message: 'Logs fetched successfully',
        data: logs,
      };
    } catch (error) {
      return {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR, // 500 for server errors
        message: 'Error fetching logs',
        error: error.message,
      };
    }
  }

  //No
  @Get('logs')
  async getLogsByResource(@Query('resource') resource: string) {
    try {
      const logs = await this.logsService.getLogsByResource(resource);

      return {
        statusCode: HttpStatus.OK,
        message: 'Logs fetched successfully by resource',
        data: logs,
      };
    } catch (error) {
      return {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Error fetching logs by resource',
        error: error.message,
      };
    }
  }

  
  @UseGuards(JwtAuthGuard)
  @Get('dynamic-search')
  async searchWithDynamicFilters(
    @Query('filters') filters: string,
    @Query('skip') skip: string,
    @Query('limit') limit: string,
    @Query('sort') sort: string,
    @Query('search') search: string,
    @Query('userName') userName: string,
    @Query('metadata') metadata: string,
    @Query('serviceKey') service: string,
    @Query('userId') userId?: string,
  ) {
    try {
      const parsedFilters = filters ? JSON.parse(filters) : {};
      const skipNum = skip ? Number(skip) : 0;
      const limitNum = limit ? Number(limit) : 10;
      const serviceKey = Number(service);

      // console.log('Input parameters:', { filters, userName, metadata, search, userId });

      // Add individual search parameters to parsedFilters - let service handle the query building
      if (userId) {
        parsedFilters.userId = userId;
      }

      if (userName) {
        parsedFilters.userName = userName;
      }

      if (metadata) {
        parsedFilters.metadata = metadata;
      }

      if (search) {
        parsedFilters.search = search;
      }

      // console.log('Final parsedFilters sent to service:', JSON.stringify(parsedFilters, null, 2));

      const logs = await this.logsService.findWithDynamicFilters(parsedFilters, skipNum, limitNum, sort || 'createdAt', serviceKey);

      return {
        statusCode: HttpStatus.OK,
        message: 'Logs fetched successfully with dynamic filters',
        data: logs,
      };
    } catch (error) {
      return {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Error fetching logs with dynamic filters',
        error: error.message,
      };
    }
  }

  @UseGuards(JwtAuthGuard)
  @Get('filters-list')
  async getFilters(@Query('serviceKey') serviceKey: string, @Query('schoolId') schoolId?: string) {
    try {
      // Fetch filters from the logs service
      const schoolFilter = await this.logsService.getSchoolFilter(serviceKey);
      const actionFilter = await this.logsService.getActionFilter(serviceKey);
      const resourceFilter = await this.logsService.getResourceFilter(serviceKey);
      const userFilter = await this.logsService.getUserFilter(serviceKey, schoolId);

      return {
        statusCode: HttpStatus.OK,
        message: 'filters fetched successfully',
        filters: {
          schoolFilter,
          actionFilter,
          resourceFilter,
          userFilter,
        },
      };
    } catch (error) {
      return {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Error fetching filters',
        error: error.message,
      };
    }
  }


}
