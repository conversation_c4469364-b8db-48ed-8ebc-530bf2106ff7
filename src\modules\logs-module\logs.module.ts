import { Module ,MiddlewareConsumer} from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { LogsService } from './logs.service';
import { LogsController } from './logs.controller';
import { AuditLog, AuditLogSchema } from './schemas/audit-log.schema';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { RadiologyLog, RadiologySchema } from './schemas/radiology-log.schema';


@Module({
  imports: [
    MongooseModule.forFeature([{ name: AuditLog.name, schema: AuditLogSchema }]),
    //Implementation here
    MongooseModule.forFeature([{name: RadiologyLog.name, schema: RadiologySchema }], 
      'radiologyConnection'
    ),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_ACCESS_SECRET'),
        signOptions: { expiresIn: '1h' },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [LogsController],
  providers: [LogsService],
  exports: [LogsService], // Export if used in other modules
})
export class LogsModule {
  // configure(consumer: MiddlewareConsumer) {
  //   consumer
  //     .apply(ApiKeyMiddleware)
  //     .forRoutes(LogsController); // Apply to all routes in LogsController
  // }
}
