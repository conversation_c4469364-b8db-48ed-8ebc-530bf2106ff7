import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Schema as MongooseSchema, Document } from 'mongoose';


@Schema({
    timestamps: true,
    strict: false
})
export class RadiologyLog extends Document {
    @Prop({required: true })
    userId: string;

    @Prop({ required: true})
    action: string;

    @Prop({ required: true })
    resource: string;

    @Prop({ type: MongooseSchema.Types.Mixed }) // Allows dynamic nested objects
    metadata: Record<string, any>;


}

export const RadiologySchema = SchemaFactory.createForClass(RadiologyLog)