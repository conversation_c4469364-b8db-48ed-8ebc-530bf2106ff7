import { Body, Controller, Post, Put, Param } from '@nestjs/common';
import { RegisterClientService } from './register-client.service';
import { Client } from './schemas/client.schema';

@Controller('register-client')
export class RegisterClientController {
  constructor(private readonly registerClientService: RegisterClientService) {}

  @Post()
  async registerClient(
    @Body('applicationName') applicationName: string,
    @Body('description') description?: string,
  ): Promise<Client> {
    return await this.registerClientService.registerClient(applicationName, description);
  }

  @Put(':apiKey')
  async updateClient(
    @Param('apiKey') apiKey: string,
    @Body() updates: Partial<Client>,
  ): Promise<Client | null> {
    return await this.registerClientService.updateClient(apiKey, updates);
  }

  @Put(':apiKey/deactivate')
  async deactivateClient(@Param('apiKey') apiKey: string): Promise<Client | null> {
    return await this.registerClientService.deactivateClient(apiKey);
  }
}
