import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class Client extends Document {
  @Prop({ required: true, unique: true })
  apiKey: string;

  @Prop({ required: true })
  applicationName: string;

  @Prop({ required: true, enum: ['ACTIVE', 'INACTIVE'], default: 'ACTIVE' })
  status: string;

  @Prop()
  description?: string;
}

export const ClientSchema = SchemaFactory.createForClass(Client);
