import { Module ,MiddlewareConsumer} from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AuthorizationService } from './ouath.service';
import { OauthController } from './oauth.controller';
import { JwtModule } from '@nestjs/jwt';
import { LogsService } from '../modules/logs-module/logs.service';
import { Authorization, AuthorizationSchema } from './schemas/authorization.schema';
import { LogsModule } from '../modules/logs-module/logs.module';
@Module({
  imports: [
    MongooseModule.forFeature([{ name: Authorization.name, schema: AuthorizationSchema }]),
    JwtModule.register({
      secret: 'xyz', // Replace with your secret key
      signOptions: { expiresIn: '60s' }, // Replace with your desired options
    }),
    LogsModule
  ],
  controllers: [OauthController],
  providers: [AuthorizationService,LogsModule],
  exports: [AuthorizationService], // Export if used in other modules
})
export class OauthModule {
  // configure(consumer: MiddlewareConsumer) {
  //   consumer
  //     .apply(ApiKeyMiddleware)
  //     .forRoutes(LogsController); // Apply to all routes in LogsController
  // }
}
