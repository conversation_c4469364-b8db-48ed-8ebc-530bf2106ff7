import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Authorization } from './schemas/authorization.schema';

@Injectable()
export class AuthorizationService {
  constructor(
    @InjectModel('Authorization') private readonly authModel: Model<Authorization>,
  ) {}

  async saveAuthCode(authCodeData: Partial<Authorization>): Promise<Authorization> {
    const authCode = new this.authModel(authCodeData);
    return authCode.save();
  }

  async findAuthCode(authCode: string): Promise<Authorization | null> {
    return this.authModel.findOne({ authCode, isExpired: false }).exec();
  }

  async expireAuthCode(authCode: string): Promise<void> {
    await this.authModel.updateOne({ authCode }, { isExpired: true }).exec();
  }
  
}
