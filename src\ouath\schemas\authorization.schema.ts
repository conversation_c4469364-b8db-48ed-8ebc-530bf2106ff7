import { <PERSON><PERSON>, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Schema as MongooseSchema, Document } from 'mongoose';

@Schema({
  timestamps: true, // Adds createdAt and updatedAt fields
  strict: false,    // Allows additional fields at the root level
})
export class Authorization  extends Document {
  @Prop({ required: true })
  authCode: string;

  @Prop({ required: false })
  school_name: string;

  @Prop({ required: true })
  school_id: string;

  @Prop({ required: true })
  state: string;

  @Prop({ required: false })
  isExpired: boolean;

  @Prop({ required: false })
  ipAddress: string;



  // Additional root-level dynamic keys are allowed by `strict: false`
}

export const AuthorizationSchema  = SchemaFactory.createForClass(Authorization);
